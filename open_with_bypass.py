#!/usr/bin/env python3
"""
Auto-open Billy'S Upscaler with ngrok warning bypass
"""

import webbrowser
import time
import requests
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

def open_with_selenium_bypass(url):
    """Open URL with Selenium and auto-bypass warning"""
    
    print("🤖 Opening with Selenium auto-bypass...")
    
    try:
        # Setup Chrome options
        chrome_options = Options()
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # Add custom user agent
        chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 BillyUpscaler/1.0')
        
        # Create driver
        driver = webdriver.Chrome(options=chrome_options)
        
        # Execute script to add bypass headers
        driver.execute_cdp_cmd('Network.setUserAgentOverride', {
            "userAgent": "Mozilla/5.0 (BillyUpscaler) Bypass/1.0"
        })
        
        # Navigate to URL
        driver.get(url)
        
        # Wait a bit for page to load
        time.sleep(2)
        
        # Check if we're on warning page
        try:
            # Look for warning page indicators
            warning_indicators = [
                "Are you the developer?",
                "Visit Site",
                "ngrok-skip-browser-warning"
            ]
            
            page_text = driver.page_source
            is_warning_page = any(indicator in page_text for indicator in warning_indicators)
            
            if is_warning_page:
                print("⚠️  Warning page detected, attempting auto-bypass...")
                
                # Try to find and click Visit Site button
                try:
                    # Multiple selectors for the button
                    button_selectors = [
                        "//button[contains(text(), 'Visit Site')]",
                        "//a[contains(text(), 'Visit Site')]",
                        "//input[@value='Visit Site']",
                        "//button[@type='submit']",
                        "//input[@type='submit']"
                    ]
                    
                    for selector in button_selectors:
                        try:
                            button = WebDriverWait(driver, 2).until(
                                EC.element_to_be_clickable((By.XPATH, selector))
                            )
                            print(f"✅ Found button with selector: {selector}")
                            button.click()
                            print("🎯 Clicked Visit Site button")
                            break
                        except:
                            continue
                    
                    # Wait for redirect
                    time.sleep(3)
                    
                except Exception as e:
                    print(f"❌ Could not find/click button: {e}")
                    
                    # Fallback: inject bypass script
                    bypass_script = """
                    // Add bypass headers to all requests
                    const originalFetch = window.fetch;
                    window.fetch = function() {
                        const args = Array.prototype.slice.call(arguments);
                        if (args[1]) {
                            args[1].headers = args[1].headers || {};
                        } else {
                            args[1] = { headers: {} };
                        }
                        args[1].headers['ngrok-skip-browser-warning'] = 'true';
                        return originalFetch.apply(this, args);
                    };
                    
                    // Redirect to main app
                    window.location.href = '/';
                    """
                    
                    driver.execute_script(bypass_script)
                    time.sleep(2)
            
            else:
                print("✅ No warning page detected, app loaded successfully")
            
        except Exception as e:
            print(f"❌ Error during bypass: {e}")
        
        print("🌐 Browser opened with bypass. You can now use the application.")
        print("Press Enter to close browser...")
        input()
        
        driver.quit()
        
    except ImportError:
        print("❌ Selenium not installed. Install with: pip install selenium")
        print("🔄 Falling back to regular browser opening...")
        return False
    except Exception as e:
        print(f"❌ Selenium bypass failed: {e}")
        return False
    
    return True

def open_with_requests_bypass(url):
    """Try to bypass using requests first, then open browser"""
    
    print("🔄 Attempting requests-based bypass...")
    
    bypass_headers = {
        'ngrok-skip-browser-warning': 'true',
        'User-Agent': 'Mozilla/5.0 (BillyUpscaler) Bypass/1.0',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate, br',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    }
    
    try:
        # Make request with bypass headers
        response = requests.get(url, headers=bypass_headers, timeout=10)
        
        if response.status_code == 200:
            if 'Billy\'S Upscaler' in response.text:
                print("✅ Bypass successful via requests")
                # Now open in browser - should be bypassed
                webbrowser.open(url)
                return True
            elif 'Are you the developer?' in response.text:
                print("⚠️  Still getting warning page")
                return False
        
    except Exception as e:
        print(f"❌ Requests bypass failed: {e}")
    
    return False

def create_bypass_bookmark():
    """Generate a bookmark for manual bypass"""
    
    bookmarklet = """javascript:(function(){
        if(window.location.hostname.includes('ngrok')){
            // Try to click Visit Site button
            const buttons = document.querySelectorAll('button, a, input[type="submit"]');
            for(let btn of buttons){
                if(btn.textContent.includes('Visit Site') || btn.textContent.includes('Visit')){
                    btn.click();
                    return;
                }
            }
            // Fallback: reload with bypass headers
            fetch(window.location.href, {
                headers: {'ngrok-skip-browser-warning': 'true'}
            }).then(() => window.location.reload());
        }
    })();"""
    
    print("\n📎 Manual Bypass Bookmark:")
    print("Copy this to your browser bookmarks:")
    print(bookmarklet)
    print("\nTo use: Click the bookmark when you see the ngrok warning page")

def main():
    """Main function"""
    
    print("🚀 Billy'S Upscaler - Ngrok Bypass Opener")
    print("=" * 50)
    
    # Get URL
    url = input("Enter ngrok URL (or press Enter for default): ").strip()
    
    if not url:
        url = "https://423b571923de.ngrok-free.app"  # Default from your screenshot
    
    if not url.startswith('http'):
        url = f"https://{url}"
    
    print(f"\n🎯 Target URL: {url}")
    
    # Menu
    print("\nChoose bypass method:")
    print("1. Auto-bypass with Selenium (recommended)")
    print("2. Requests bypass + browser")
    print("3. Regular browser (manual bypass)")
    print("4. Generate bypass bookmark")
    print("5. Open bypass landing page")
    
    choice = input("\nEnter choice (1-5): ").strip()
    
    if choice == "1":
        if not open_with_selenium_bypass(url):
            print("🔄 Selenium failed, trying requests bypass...")
            if not open_with_requests_bypass(url):
                print("🔄 Opening regular browser...")
                webbrowser.open(url)
    
    elif choice == "2":
        if not open_with_requests_bypass(url):
            print("🔄 Requests bypass failed, opening regular browser...")
            webbrowser.open(url)
    
    elif choice == "3":
        print("🌐 Opening in regular browser...")
        webbrowser.open(url)
        print("📝 Manually click 'Visit Site' if you see the warning")
    
    elif choice == "4":
        create_bypass_bookmark()
    
    elif choice == "5":
        bypass_url = url + "/bypass"
        print(f"🌐 Opening bypass landing page: {bypass_url}")
        webbrowser.open(bypass_url)
    
    else:
        print("❌ Invalid choice")

if __name__ == "__main__":
    main()
