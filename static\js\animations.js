// Animations for BillUpscaler
document.addEventListener('DOMContentLoaded', function() {
  // Page loader animation
  const pageLoader = document.querySelector('.page-loader');
  const loaderCircle = document.querySelector('.loader-circle');
  const mainContent = document.querySelector('.main-content');
  
  // Animate the loader circle
  anime({
    targets: loaderCircle,
    rotate: '1turn',
    duration: 1000,
    loop: true,
    easing: 'linear'
  });
  
  // Hide loader and show content after a delay
  setTimeout(function() {
    // Fade out the loader
    anime({
      targets: pageLoader,
      opacity: [1, 0],
      duration: 800,
      easing: 'easeOutExpo',
      complete: function() {
        pageLoader.style.display = 'none';
        
        // Fade in the main content
        anime({
          targets: mainContent,
          opacity: [0, 1],
          duration: 800,
          easing: 'easeOutExpo'
        });
        
        // Animate all elements with the animate-element class
        animateElements();
      }
    });
  }, 1500);
  
  // Function to animate elements with the animate-element class
  function animateElements() {
    const animatedElements = document.querySelectorAll('.animate-element');
    
    animatedElements.forEach(function(element, index) {
      const delay = element.dataset.delay ? parseInt(element.dataset.delay) : index * 100;
      
      anime({
        targets: element,
        opacity: [0, 1],
        translateY: [20, 0],
        delay: delay,
        duration: 800,
        easing: 'easeOutExpo'
      });
    });
  }
  
  // Add hover animations for buttons
  const buttons = document.querySelectorAll('.btn');
  
  buttons.forEach(function(button) {
    button.addEventListener('mouseenter', function() {
      anime({
        targets: this,
        scale: 1.05,
        duration: 300,
        easing: 'easeOutExpo'
      });
    });
    
    button.addEventListener('mouseleave', function() {
      anime({
        targets: this,
        scale: 1,
        duration: 300,
        easing: 'easeOutExpo'
      });
    });
  });
  
  // Add hover animations for cards
  const cards = document.querySelectorAll('.card');
  
  cards.forEach(function(card) {
    card.addEventListener('mouseenter', function() {
      anime({
        targets: this,
        translateY: -10,
        boxShadow: '0 15px 30px rgba(255, 0, 0, 0.3)',
        duration: 300,
        easing: 'easeOutExpo'
      });
    });
    
    card.addEventListener('mouseleave', function() {
      anime({
        targets: this,
        translateY: 0,
        boxShadow: '0 4px 16px rgba(255, 0, 0, 0.2)',
        duration: 300,
        easing: 'easeOutExpo'
      });
    });
  });
});