{% extends 'base.html' %}

{% block content %}
<div class="result-container">
    <h2>Hasil Upscaling</h2>
    
    <div class="model-info">
        <p>Model yang digunakan: <strong>{{ model_used }}</strong></p>
    </div>
    
    <div class="comparison-slider-wrapper">
        <div id="comparison-slider" class="comparison-slider">
            <div class="before-image">
                <img src="{{ input_image }}" alt="Gambar Asli">
            </div>
            <div class="after-image">
                <img src="{{ output_image }}" alt="Gambar Hasil Upscale">
            </div>
            <div class="slider-handle"></div>
        </div>
        <div class="slider-labels">
            <span class="label-before">Asli</span>
            <span class="label-after">Hasil Upscale</span>
        </div>
    </div>
    
    <div class="download-container">
        <a href="{{ download_url }}" class="download-button" download>
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                <polyline points="7 10 12 15 17 10"></polyline>
                <line x1="12" y1="15" x2="12" y2="3"></line>
            </svg>
            <span>Download Hasil</span>
        </a>
    </div>
    
    <div class="action-buttons">
        <a href="/" class="back-button">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <line x1="19" y1="12" x2="5" y2="12"></line>
                <polyline points="12 19 5 12 12 5"></polyline>
            </svg>
            <span>Upscale {% if is_video %}Video{% else %}Gambar{% endif %} Lain</span>
        </a>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const slider = document.getElementById('comparison-slider');
        if (!slider) return;
        
        const beforeImage = slider.querySelector('.before-image');
        const afterImage = slider.querySelector('.after-image');
        const sliderHandle = slider.querySelector('.slider-handle');
        
        let isDragging = false;
        
        // Fungsi untuk mengatur posisi slider
        function setSliderPosition(position) {
            const sliderWidth = slider.offsetWidth;
            let newPosition = position;
            
            // Batasi posisi dalam rentang slider
            if (newPosition < 0) newPosition = 0;
            if (newPosition > sliderWidth) newPosition = sliderWidth;
            
            // Hitung persentase posisi
            const percentage = (newPosition / sliderWidth) * 100;
            
            // Atur lebar gambar setelah
            afterImage.style.width = `${percentage}%`;
            
            // Posisikan handle
            sliderHandle.style.left = `${percentage}%`;
        }
        
        // Event listener untuk mouse down pada handle
        sliderHandle.addEventListener('mousedown', function(e) {
            isDragging = true;
            e.preventDefault();
        });
        
        // Event listener untuk touch start pada handle
        sliderHandle.addEventListener('touchstart', function(e) {
            isDragging = true;
        });
        
        // Event listener untuk mouse move
        document.addEventListener('mousemove', function(e) {
            if (!isDragging) return;
            
            const sliderRect = slider.getBoundingClientRect();
            const position = e.clientX - sliderRect.left;
            
            setSliderPosition(position);
        });
        
        // Event listener untuk touch move
        document.addEventListener('touchmove', function(e) {
            if (!isDragging || !e.touches[0]) return;
            
            const sliderRect = slider.getBoundingClientRect();
            const position = e.touches[0].clientX - sliderRect.left;
            
            setSliderPosition(position);
        });
        
        // Event listener untuk mouse up
        document.addEventListener('mouseup', function() {
            isDragging = false;
        });
        
        // Event listener untuk touch end
        document.addEventListener('touchend', function() {
            isDragging = false;
        });
        
        // Event listener untuk klik pada slider
        slider.addEventListener('click', function(e) {
            const sliderRect = slider.getBoundingClientRect();
            const position = e.clientX - sliderRect.left;
            
            setSliderPosition(position);
        });
        
        // Set posisi awal (50%)
        setSliderPosition(slider.offsetWidth / 2);
    });
</script>
{% endblock %}

{% block styles %}
<style>
    .comparison-slider-wrapper {
        position: relative;
        width: 100%;
        max-width: 800px;
        margin: 2rem auto;
        overflow: hidden;
    }
    
    .comparison-slider {
        position: relative;
        width: 100%;
        height: 400px;
        border: 2px solid var(--primary-color);
        border-radius: 8px;
        overflow: hidden;
        cursor: ew-resize;
    }
    
    .before-image, .after-image {
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        overflow: hidden;
    }
    
    .before-image {
        width: 100%; /* Make sure before-image covers the entire container */
        z-index: 1; /* Lower z-index so it stays behind */
    }
    
    .after-image {
        width: 50%;
        z-index: 2; /* Higher z-index to appear on top */
        border-right: 2px solid var(--primary-color);
    }
    
    .before-image img, .after-image img {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: contain;
    }
    
    .slider-handle {
        position: absolute;
        top: 0;
        left: 50%;
        width: 4px;
        height: 100%;
        background-color: var(--primary-color);
        cursor: ew-resize;
        transform: translateX(-50%);
        z-index: 3; /* Highest z-index to stay on top */
    }
    
    .slider-handle::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 30px;
        height: 30px;
        background-color: var(--primary-color);
        border-radius: 50%;
    }
    
    .slider-handle::after {
        content: '⟷';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: white;
        font-size: 16px;
        font-weight: bold;
    }
    
    .slider-labels {
        display: flex;
        justify-content: space-between;
        margin-top: 0.5rem;
        font-size: 0.9rem;
        color: var(--text-secondary);
    }
    
    .download-container {
        text-align: center;
        margin: 2rem 0;
    }
</style>
{% endblock %}
