import os
import torch
from basicsr.archs.rrdbnet_arch import RRDBNet
from realesrgan import RealESRGANer
import urllib.request
import shutil

# Pastikan direktori weights ada
os.makedirs('weights', exist_ok=True)

print("Memperbaiki masalah loading model RealESRGAN...")
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

# Definisikan model yang bermasalah
model_name = 'realesrgan-x4plus'
model_url = 'https://github.com/xinntao/Real-ESRGAN/releases/download/v0.1.0/RealESRGAN_x4plus.pth'
model_path = os.path.join('weights', 'RealESRGAN_x4plus.pth')
model = RRDBNet(num_in_ch=3, num_out_ch=3, num_feat=64, num_block=23, num_grow_ch=32, scale=4)

# Download model jika belum ada
if not os.path.exists(model_path):
    print(f"Mendownload model dari {model_url}...")
    try:
        with urllib.request.urlopen(model_url) as response, open(model_path, 'wb') as out_file:
            shutil.copyfileobj(response, out_file)
        print("Download selesai!")
    except Exception as e:
        print(f"Gagal mendownload model: {e}")
        print("Silakan download manual dari URL dan simpan di folder 'weights'")

try:
    # Coba load model tanpa parameter strict
    upsampler = RealESRGANer(
        scale=4,
        model_path=model_path,  # Gunakan path lokal, bukan URL
        model=model,
        tile=512,
        tile_pad=16,
        pre_pad=10,
        half=(device == 'cuda'),
        device=device
    )
    
    # Tambahkan penanganan error khusus
    if hasattr(upsampler, 'model') and upsampler.model is not None:
        print(f"Model {model_name} berhasil dimuat!")
    else:
        print(f"Model {model_name} gagal dimuat, tetapi tidak ada error yang dilempar.")
        
except Exception as e:
    print(f"Error saat memuat model: {e}")
    print("Mencoba metode alternatif...")
    
    try:
        # Metode alternatif: load model secara manual
        state_dict = torch.load(model_path, map_location=device)
        # Filter state_dict untuk hanya menggunakan key yang ada di model
        model_dict = model.state_dict()
        matched_dict = {k: v for k, v in state_dict.items() if k in model_dict}
        model_dict.update(matched_dict)
        model.load_state_dict(model_dict, strict=False)
        
        # Buat upsampler baru dengan model yang sudah dimuat
        upsampler = RealESRGANer(
            scale=4,
            model=model,
            tile=512,
            tile_pad=16,
            pre_pad=10,
            half=(device == 'cuda'),
            device=device
        )
        print("Model berhasil dimuat dengan metode alternatif!")
    except Exception as e2:
        print(f"Metode alternatif juga gagal: {e2}")
        print("Silakan gunakan model lain atau periksa instalasi RealESRGAN Anda.")

print("Selesai memeriksa model.")