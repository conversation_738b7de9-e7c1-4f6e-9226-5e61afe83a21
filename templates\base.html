<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BillUpscaler</title>
    <link rel="stylesheet" href="{{ url_for('static', path='css/main.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', path='css/loading.css') }}">
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', path='favicon.ico') }}">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    {% block styles %}{% endblock %}
</head>
<body>
    <div class="container">
        <header>
            <h1>Billy'S Upscaler</h1>
            <p class="tagline">Tingkatkan kualitas gambar dengan AI</p>
            <nav class="main-nav">
                <a href="/" class="nav-link">Home</a>
                <a href="/about" class="nav-link">About</a>
            </nav>
            <hr>
        </header>
        
        <main>
            {% block content %}{% endblock %}
        </main>
        
        <footer>
            <p>Billy'S Upscaler - Powered by <a href="https://github.com/xinntao/Real-ESRGAN" target="_blank">Real-ESRGAN</a></p>
        </footer>
    </div>

    <!-- Loading overlay -->
    <div id="loading-overlay" class="loading-overlay" style="display: none;">
        <div class="loading-spinner"></div>
        <p>Memproses gambar... Mohon tunggu.</p>
    </div>



    <script src="{{ url_for('static', path='js/preview.js') }}"></script>
</script>
</script>
</script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const modelSelect = document.getElementById('model-select');
            const modelDescription = document.getElementById('model-description');
            
            if (modelSelect && modelDescription) {
                modelSelect.addEventListener('change', function() {
                    const selectedOption = modelSelect.options[modelSelect.selectedIndex];
                    const description = selectedOption.text.split(' - ')[1];
                    modelDescription.textContent = description || '';
                });
            }
        });
    </script>
    {% block extra_scripts %}{% endblock %}
</body>
</html>
    