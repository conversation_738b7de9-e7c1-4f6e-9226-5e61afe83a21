<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="ngrok-skip-browser-warning" content="true">
    <meta http-equiv="X-Ngrok-Skip-Browser-Warning" content="true">
    <title>BillUpscaler</title>
    <link rel="stylesheet" href="{{ url_for('static', path='css/main.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', path='css/loading.css') }}">
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', path='favicon.ico') }}">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    {% block styles %}{% endblock %}
</head>
<body>
    <div class="container">
        <header>
            <h1>Billy'S Upscaler</h1>
            <p class="tagline">Tingkatkan kualitas gambar dengan AI</p>
            <nav class="main-nav">
                <a href="/" class="nav-link">Home</a>
                <a href="/about" class="nav-link">About</a>
            </nav>
            <hr>
        </header>
        
        <main>
            {% block content %}{% endblock %}
        </main>
        
        <footer>
            <p>Billy'S Upscaler - Powered by <a href="https://github.com/xinntao/Real-ESRGAN" target="_blank">Real-ESRGAN</a></p>
        </footer>
    </div>

    <!-- Loading overlay -->
    <div id="loading-overlay" class="loading-overlay" style="display: none;">
        <div class="loading-spinner"></div>
        <p>Memproses gambar... Mohon tunggu.</p>
    </div>

    <!-- Script untuk auto-bypass ngrok warning -->
    <script>
        // Immediate execution to bypass ngrok warning
        (function() {
            // Function to auto-click Visit Site button
            function autoClickVisitSite() {
                // Multiple selectors for the Visit Site button
                const selectors = [
                    'a[href*="visit"]',
                    'button[type="submit"]',
                    'input[type="submit"]',
                    'a:contains("Visit Site")',
                    '.btn',
                    '[onclick*="visit"]',
                    'a[href="#"]',
                    'button'
                ];

                for (let selector of selectors) {
                    const button = document.querySelector(selector);
                    if (button && (
                        button.textContent.includes('Visit Site') ||
                        button.textContent.includes('Visit') ||
                        button.value === 'Visit Site' ||
                        button.innerHTML.includes('Visit')
                    )) {
                        console.log('Auto-clicking Visit Site button');
                        button.click();
                        return true;
                    }
                }
                return false;
            }

            // Function to check if we're on warning page
            function isNgrokWarningPage() {
                const bodyText = document.body ? document.body.textContent : '';
                return bodyText.includes('Are you the developer?') ||
                       bodyText.includes('Visit Site') ||
                       bodyText.includes('ngrok-skip-browser-warning') ||
                       bodyText.includes('prevent abuse');
            }

            // Function to bypass warning
            function bypassWarning() {
                if (isNgrokWarningPage()) {
                    console.log('Ngrok warning page detected, attempting bypass...');

                    // Method 1: Auto-click Visit Site button
                    if (autoClickVisitSite()) {
                        return;
                    }

                    // Method 2: Form submission
                    const form = document.querySelector('form');
                    if (form) {
                        console.log('Submitting form to bypass warning');
                        form.submit();
                        return;
                    }

                    // Method 3: Redirect with bypass parameters
                    const currentUrl = window.location.href;
                    const bypassUrl = currentUrl.includes('?')
                        ? currentUrl + '&ngrok-skip-browser-warning=true'
                        : currentUrl + '?ngrok-skip-browser-warning=true';

                    console.log('Redirecting with bypass parameters');
                    window.location.href = bypassUrl;
                }
            }

            // Execute bypass immediately if DOM is ready
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', bypassWarning);
            } else {
                bypassWarning();
            }

            // Also try after a short delay
            setTimeout(bypassWarning, 100);
            setTimeout(bypassWarning, 500);
            setTimeout(bypassWarning, 1000);

            // Override requests for future use
            if (window.location.hostname.includes('ngrok')) {
                // Override XMLHttpRequest
                if (typeof XMLHttpRequest !== 'undefined') {
                    const originalOpen = XMLHttpRequest.prototype.open;
                    XMLHttpRequest.prototype.open = function() {
                        originalOpen.apply(this, arguments);
                        this.setRequestHeader('ngrok-skip-browser-warning', 'true');
                        this.setRequestHeader('User-Agent', 'Mozilla/5.0 (BillyUpscaler) Custom/1.0');
                    };
                }

                // Override fetch
                if (typeof fetch !== 'undefined') {
                    const originalFetch = window.fetch;
                    window.fetch = function() {
                        const args = Array.prototype.slice.call(arguments);
                        if (args[1]) {
                            args[1].headers = args[1].headers || {};
                        } else {
                            args[1] = { headers: {} };
                        }
                        args[1].headers['ngrok-skip-browser-warning'] = 'true';
                        args[1].headers['User-Agent'] = 'Mozilla/5.0 (BillyUpscaler) Custom/1.0';
                        return originalFetch.apply(this, args);
                    };
                }
            }
        })();
    </script>

    <script src="{{ url_for('static', path='js/preview.js') }}"></script>
    <script src="{{ url_for('static', path='js/animations.js') }}"></script>
    <script src="{{ url_for('static', path='js/theme.js') }}"></script>
</script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const modelSelect = document.getElementById('model-select');
            const modelDescription = document.getElementById('model-description');
            
            if (modelSelect && modelDescription) {
                modelSelect.addEventListener('change', function() {
                    const selectedOption = modelSelect.options[modelSelect.selectedIndex];
                    const description = selectedOption.text.split(' - ')[1];
                    modelDescription.textContent = description || '';
                });
            }
        });
    </script>
    {% block extra_scripts %}{% endblock %}
</body>
</html>
    