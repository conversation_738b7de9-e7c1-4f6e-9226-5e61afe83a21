#!/usr/bin/env python3
"""
Black Box Testing Script for Billy'S Upscaler
Automated testing of web application functionality
"""

import requests
import time
import os
from pathlib import Path
import json

class BillyUpscalerTester:
    def __init__(self, base_url):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.test_results = []
        
    def log_test(self, test_name, status, message="", duration=0):
        """Log test results"""
        result = {
            "test_name": test_name,
            "status": status,  # PASS, FAIL, SKIP
            "message": message,
            "duration": duration,
            "timestamp": time.time()
        }
        self.test_results.append(result)
        print(f"[{status}] {test_name}: {message}")
        
    def test_homepage_loading(self):
        """Test Case 1.1: Homepage Loading"""
        start_time = time.time()
        try:
            response = self.session.get(f"{self.base_url}/")
            duration = time.time() - start_time
            
            if response.status_code == 200:
                if "Billy'S Upscaler" in response.text:
                    self.log_test("Homepage Loading", "PASS", 
                                f"Page loaded in {duration:.2f}s", duration)
                else:
                    self.log_test("Homepage Loading", "FAIL", 
                                "Title not found in response")
            else:
                self.log_test("Homepage Loading", "FAIL", 
                            f"HTTP {response.status_code}")
        except Exception as e:
            self.log_test("Homepage Loading", "FAIL", str(e))
            
    def test_about_page(self):
        """Test Case 1.2: About Page Navigation"""
        start_time = time.time()
        try:
            response = self.session.get(f"{self.base_url}/about")
            duration = time.time() - start_time
            
            if response.status_code == 200:
                if "Real-ESRGAN" in response.text and "Credits" in response.text:
                    self.log_test("About Page", "PASS", 
                                f"About page loaded in {duration:.2f}s", duration)
                else:
                    self.log_test("About Page", "FAIL", 
                                "Expected content not found")
            else:
                self.log_test("About Page", "FAIL", f"HTTP {response.status_code}")
        except Exception as e:
            self.log_test("About Page", "FAIL", str(e))
            
    def test_file_upload_validation(self):
        """Test Case 2.3: Invalid File Types"""
        # Create a test text file
        test_file_content = "This is not an image"
        
        try:
            files = {'file': ('test.txt', test_file_content, 'text/plain')}
            data = {'model': 'realesrgan-x4plus'}
            
            response = self.session.post(f"{self.base_url}/upscale", 
                                       files=files, data=data)
            
            if "Tipe file tidak valid" in response.text:
                self.log_test("File Upload Validation", "PASS", 
                            "Invalid file type rejected")
            else:
                self.log_test("File Upload Validation", "FAIL", 
                            "Invalid file type not rejected")
        except Exception as e:
            self.log_test("File Upload Validation", "FAIL", str(e))
            
    def test_image_processing(self, image_path):
        """Test Case 4.1: Standard Image Upscaling"""
        if not os.path.exists(image_path):
            self.log_test("Image Processing", "SKIP", 
                        f"Test image not found: {image_path}")
            return
            
        start_time = time.time()
        try:
            with open(image_path, 'rb') as f:
                files = {'file': (os.path.basename(image_path), f, 'image/png')}
                data = {'model': 'realesrgan-x4plus'}
                
                response = self.session.post(f"{self.base_url}/upscale", 
                                           files=files, data=data, timeout=300)
                
            duration = time.time() - start_time
            
            if response.status_code == 200:
                if "download" in response.text.lower():
                    self.log_test("Image Processing", "PASS", 
                                f"Image processed in {duration:.2f}s", duration)
                else:
                    self.log_test("Image Processing", "FAIL", 
                                "No download link found")
            else:
                self.log_test("Image Processing", "FAIL", 
                            f"HTTP {response.status_code}")
        except Exception as e:
            self.log_test("Image Processing", "FAIL", str(e))
            
    def test_model_selection(self):
        """Test Case 3.1: Model Selection Dropdown"""
        try:
            response = self.session.get(f"{self.base_url}/")
            
            expected_models = [
                "realesrgan-x4plus",
                "realesrgan-x2plus", 
                "realesrgan-anime-x4",
                "realesrnet-x4plus",
                "realesr-general-x4v3"
            ]
            
            models_found = 0
            for model in expected_models:
                if model in response.text:
                    models_found += 1
                    
            if models_found == len(expected_models):
                self.log_test("Model Selection", "PASS", 
                            f"All {models_found} models available")
            else:
                self.log_test("Model Selection", "FAIL", 
                            f"Only {models_found}/{len(expected_models)} models found")
        except Exception as e:
            self.log_test("Model Selection", "FAIL", str(e))
            
    def test_no_file_upload(self):
        """Test Case 6.1: No File Selected"""
        try:
            data = {'model': 'realesrgan-x4plus'}
            response = self.session.post(f"{self.base_url}/upscale", data=data)
            
            if "Tidak ada file yang dipilih" in response.text:
                self.log_test("No File Upload", "PASS", 
                            "Proper error for missing file")
            else:
                self.log_test("No File Upload", "FAIL", 
                            "Missing file error not shown")
        except Exception as e:
            self.log_test("No File Upload", "FAIL", str(e))
            
    def test_large_file_rejection(self):
        """Test Case 2.4: File Size Limits"""
        try:
            # Create a large dummy file (6MB for image test)
            large_content = b'0' * (6 * 1024 * 1024)  # 6MB
            
            files = {'file': ('large_image.png', large_content, 'image/png')}
            data = {'model': 'realesrgan-x4plus'}
            
            response = self.session.post(f"{self.base_url}/upscale", 
                                       files=files, data=data)
            
            if "terlalu besar" in response.text.lower():
                self.log_test("Large File Rejection", "PASS", 
                            "Large file properly rejected")
            else:
                self.log_test("Large File Rejection", "FAIL", 
                            "Large file not rejected")
        except Exception as e:
            self.log_test("Large File Rejection", "FAIL", str(e))
            
    def run_all_tests(self, test_image_path=None):
        """Run all black box tests"""
        print("Starting Black Box Testing for Billy'S Upscaler")
        print("=" * 50)
        
        # UI/UX Tests
        self.test_homepage_loading()
        self.test_about_page()
        
        # Model Selection Tests
        self.test_model_selection()
        
        # File Upload Tests
        self.test_file_upload_validation()
        self.test_no_file_upload()
        self.test_large_file_rejection()
        
        # Image Processing Tests (if test image provided)
        if test_image_path:
            self.test_image_processing(test_image_path)
            
        # Generate Report
        self.generate_report()
        
    def generate_report(self):
        """Generate test report"""
        print("\n" + "=" * 50)
        print("BLACK BOX TESTING REPORT")
        print("=" * 50)
        
        total_tests = len(self.test_results)
        passed_tests = len([t for t in self.test_results if t['status'] == 'PASS'])
        failed_tests = len([t for t in self.test_results if t['status'] == 'FAIL'])
        skipped_tests = len([t for t in self.test_results if t['status'] == 'SKIP'])
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {failed_tests}")
        print(f"Skipped: {skipped_tests}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        print("\nDetailed Results:")
        print("-" * 30)
        for result in self.test_results:
            status_symbol = "✓" if result['status'] == 'PASS' else "✗" if result['status'] == 'FAIL' else "⊝"
            print(f"{status_symbol} {result['test_name']}: {result['message']}")
            
        # Save results to JSON
        with open('test_results.json', 'w') as f:
            json.dump(self.test_results, f, indent=2)
        print(f"\nDetailed results saved to test_results.json")

if __name__ == "__main__":
    # Configuration
    BASE_URL = "https://406e54a280b4.ngrok-free.app/"  # Update this with your actual URL
    TEST_IMAGE_PATH = "static/results/0c453f00-2656-4ba4-87b4-458e2a0447b2.png"  # Path to test image

    # Run tests
    tester = BillyUpscalerTester(BASE_URL)
    tester.run_all_tests(TEST_IMAGE_PATH)
