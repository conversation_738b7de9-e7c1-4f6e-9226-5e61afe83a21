:root {
    --primary-color: #ff0000;
    --primary-hover: #cc0000;
    --bg-color: #111;
    --card-bg: #1a1a1a;
    --text-color: #fff;
    --text-secondary: #aaa;
    --border-color: #333;
    --success-color: #4caf50;
    --error-color: #f44336;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    background-color: var(--bg-color);
    color: var(--text-color);
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    margin: 0;
    padding: 0;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
    width: 100%;
    flex: 1;
}

header {
    text-align: center;
    margin-bottom: 2rem;
}

h1 {
    color: var(--primary-color);
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
    font-weight: 700;
}

.tagline {
    color: var(--text-secondary);
    font-size: 1.1rem;
    margin-bottom: 1rem;
}

h2 {
    color: var(--primary-color);
    font-size: 1.8rem;
    margin-bottom: 1rem;
    text-align: center;
}

h3 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    color: var(--text-color);
}

hr {
    border: none;
    height: 1px;
    background-color: var(--border-color);
    margin: 1.5rem 0;
}

.upload-container {
    background-color: var(--card-bg);
    border-radius: 10px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.description {
    text-align: center;
    color: var(--text-secondary);
    margin-bottom: 1.5rem;
}

.file-input-container {
    margin-bottom: 1.5rem;
}

.file-label {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border: 2px dashed var(--border-color);
    border-radius: 8px;
    padding: 2rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.file-label:hover {
    border-color: var(--primary-color);
    background-color: rgba(255, 0, 0, 0.05);
}

.file-icon {
    margin-bottom: 1rem;
    color: var(--primary-color);
}

.file-format {
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin-top: 0.5rem;
}

input[type="file"] {
    display: none;
}

.preview-container {
    margin: 1.5rem 0;
    text-align: center;
}

.preview-image-container {
    max-width: 100%;
    overflow: hidden;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

#image-preview {
    max-width: 100%;
    max-height: 300px;
    display: block;
    margin: 0 auto;
}

.submit-button {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 8px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 500;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.3s;
}

.submit-button:hover {
    background-color: var(--primary-hover);
}

.submit-button svg {
    margin-left: 0.5rem;
}

.error-message {
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--error-color);
    margin: 1rem 0;
    padding: 1rem;
    border: 1px solid var(--error-color);
    border-radius: 8px;
    background-color: rgba(244, 67, 54, 0.1);
}

.error-message svg {
    margin-right: 0.5rem;
    flex-shrink: 0;
}

/* Result page styles */
.result-container {
    background-color: var(--card-bg);
    border-radius: 10px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.comparison-container {
    display: flex;
    flex-wrap: wrap;
    gap: 2rem;
    margin: 2rem 0;
}

.image-container {
    flex: 1;
    min-width: 300px;
    text-align: center;
}

.image-wrapper {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    margin-bottom: 1rem;
}

.result-image {
    max-width: 100%;
    display: block;
}

.download-container {
    margin-top: 1rem;
}

.download-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background-color: var(--success-color);
    color: white;
    text-decoration: none;
    padding: 0.8rem 1.5rem;
    border-radius: 8px;
    font-weight: 500;
    transition: background-color 0.3s;
}

.download-button:hover {
    background-color: #3d8b40;
}

.download-button svg {
    margin-right: 0.5rem;
}

.action-buttons {
    display: flex;
    justify-content: center;
    margin-top: 2rem;
}

.back-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background-color: var(--border-color);
    color: white;
    text-decoration: none;
    padding: 0.8rem 1.5rem;
    border-radius: 8px;
    font-weight: 500;
    transition: background-color 0.3s;
}

.back-button:hover {
    background-color: #444;
}

.back-button svg {
    margin-right: 0.5rem;
}

/* Features section */
.features-container {
    display: flex;
    flex-wrap: wrap;
    gap: 2rem;
    margin: 2rem 0;
}

.feature {
    flex: 1;
    min-width: 250px;
    background-color: var(--card-bg);
    border-radius: 10px;
    padding: 1.5rem;
    text-align: center;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s;
}

.feature:hover {
    transform: translateY(-5px);
}

.feature-icon {
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.feature h3 {
    margin-bottom: 0.5rem;
}

.feature p {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

footer {
    text-align: center;
    margin-top: 3rem;
    padding-top: 1.5rem;
    border-top: 1px solid var(--border-color);
    color: var(--text-secondary);
    font-size: 0.9rem;
}

footer a {
    color: var(--primary-color);
    text-decoration: none;
}

footer a:hover {
    text-decoration: underline;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .container {
        padding: 1rem;
    }
    
    .comparison-container {
        flex-direction: column;
    }
    
    .image-container {
        min-width: 100%;
    }
    
    .comparison-slider {
        height: 300px;
    }
    
    .features-container {
        flex-direction: column;
    }
}


/* Model selection styles */
.model-selection-container {
    margin-bottom: 1.5rem;
}

.model-select {
    width: 100%;
    padding: 0.8rem;
    border-radius: 8px;
    background-color: var(--card-bg);
    color: var(--text-color);
    border: 2px solid var(--border-color);
    font-size: 1rem;
    font-family: 'Poppins', sans-serif;
    cursor: pointer;
    transition: all 0.3s ease;
}

.model-select:hover, .model-select:focus {
    border-color: var(--primary-color);
    outline: none;
}

.model-select option {
    background-color: var(--card-bg);
    color: var(--text-color);
    padding: 0.5rem;
}

.model-description {
    margin-top: 0.5rem;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.model-info {
    margin-bottom: 1.5rem;
    text-align: center;
    padding: 0.5rem;
    background-color: rgba(255, 0, 0, 0.05);
    border-radius: 8px;
}

.model-info strong {
    color: var(--primary-color);
}