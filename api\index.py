from enum import auto
from fastapi import Fast<PERSON><PERSON>, Request, File, UploadFile, Form
from fastapi.responses import HTMLResponse
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles
import os
import io
import base64
import cv2
import numpy as np
import torch
from basicsr.archs.rrdbnet_arch import RRDB<PERSON>
from realesrgan import RealESRGANer

# Initialize FastAPI app
app = FastAPI(title="BillUpscaler")

# Templates directory
templates = Jinja2Templates(directory="templates")

# Mount static files
app.mount("/static", StaticFiles(directory="static"), name="static")

# Allowed file extensions
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'webp'}

# Initialize RealESRGAN model
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

# Definisikan model-model yang tersedia
AVAILABLE_MODELS = {
    'realesrgan-x4plus': {
        'name': 'RealESRGAN x4plus',
        'scale': 4,
        'model_path': 'https://github.com/xinntao/Real-ESRGAN/releases/download/v0.1.0/RealESRGAN_x4plus.pth',
        'model': lambda: RRDBNet(num_in_ch=3, num_out_ch=3, num_feat=64, num_block=23, num_grow_ch=32, scale=4)
    },
    'realesrgan-x2plus': {
        'name': 'RealESRGAN x2plus',
        'scale': 2,
        'model_path': 'https://github.com/xinntao/Real-ESRGAN/releases/download/v0.2.1/RealESRGAN_x2plus.pth',
        'model': lambda: RRDBNet(num_in_ch=3, num_out_ch=3, num_feat=64, num_block=23, num_grow_ch=32, scale=2)
    },
    'realesrgan-anime-x4': {
        'name': 'RealESRGAN Anime x4',
        'scale': 4,
        'model_path': 'https://github.com/xinntao/Real-ESRGAN/releases/download/v0.2.2.4/RealESRGAN_x4plus_anime_6B.pth',
        'model': lambda: RRDBNet(num_in_ch=3, num_out_ch=3, num_feat=64, num_block=6, num_grow_ch=32, scale=4)
    },
    'realesrnet-x4plus': {
        'name': 'RealESRNet x4plus',
        'scale': 4,
        'model_path': 'https://github.com/xinntao/Real-ESRGAN/releases/download/v0.1.1/RealESRNet_x4plus.pth',
        'model': lambda: RRDBNet(num_in_ch=3, num_out_ch=3, num_feat=64, num_block=23, num_grow_ch=32, scale=4)
    },
    'realesr-general-x4v3': {
        'name': 'RealESRGAN General x4v3 (Video)',
        'scale': 4,
        'model_path': 'https://github.com/xinntao/Real-ESRGAN/releases/download/v0.2.5.0/realesr-general-x4v3.pth',
        'model': lambda: RRDBNet(num_in_ch=3, num_out_ch=3, num_feat=64, num_block=23, num_grow_ch=32, scale=4)
    }
}

# Default model
default_model_name = 'realesrgan-x4plus'
model_info = AVAILABLE_MODELS[default_model_name]
model = model_info['model']()
upsampler = RealESRGANer(
    scale=model_info['scale'],
    model_path=model_info['model_path'],
    model=model,
    tile=512,
    tile_pad=16,
    pre_pad=10,
    half=True,
    device=device,
    strict=False  # Add this parameter to ignore missing keys
)

# Inisialisasi dictionary untuk menyimpan model yang sudah dimuat
loaded_models = {default_model_name: upsampler}

def get_upsampler(model_name):
    """Mendapatkan atau memuat model upsampler berdasarkan nama model"""
    if model_name not in AVAILABLE_MODELS:
        model_name = default_model_name  # Default model
    
    if model_name not in loaded_models:
        model_info = AVAILABLE_MODELS[model_name]
        model = model_info['model']()
        
        loaded_models[model_name] = RealESRGANer(
            scale=model_info['scale'],
            model_path=model_info['model_path'],
            model=model,
            tile=512,
            tile_pad=16,
            pre_pad=10,
            half=True,
            device=device,
            strict=False  # Add this parameter to ignore missing keys
        )
    
    return loaded_models[model_name], AVAILABLE_MODELS[model_name]['scale']

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

# Tambahkan import untuk FileResponse dan HTTPException
from fastapi import FastAPI, Request, File, UploadFile, HTTPException
from fastapi.responses import HTMLResponse, FileResponse

# Tambahkan import untuk uuid
import uuid

# Tambahkan konfigurasi folder hasil
RESULT_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'static', 'results')

# Pastikan direktori hasil ada
os.makedirs(RESULT_FOLDER, exist_ok=True)

@app.get("/", response_class=HTMLResponse)
async def index(request: Request):
    return templates.TemplateResponse("index.html", {"request": request, "models": AVAILABLE_MODELS})

@app.post("/upscale")
async def upscale(request: Request, file: UploadFile = File(...), model: str = Form(default_model_name)):
    # Check if file exists
    if not file:
        return templates.TemplateResponse(
            "index.html", 
            {"request": request, "error": "Tidak ada file yang dipilih", "models": AVAILABLE_MODELS}
        )
    
    # Check if file extension is allowed
    if not allowed_file(file.filename):
        return templates.TemplateResponse(
            "index.html", 
            {"request": request, "error": "Tipe file tidak valid. Silakan unggah file PNG, JPG, JPEG, atau WEBP.", "models": AVAILABLE_MODELS}
        )
    
    try:
        # Read file to memory
        contents = await file.read()
        
        # Limit file size (5MB)
        if len(contents) > 5 * 1024 * 1024:
            return templates.TemplateResponse(
                "index.html", 
                {"request": request, "error": "Ukuran file terlalu besar. Maksimum 5MB.", "models": AVAILABLE_MODELS}
            )
        
        # Convert to numpy array
        nparr = np.frombuffer(contents, np.uint8)
        img = cv2.imdecode(nparr, cv2.IMREAD_UNCHANGED)
        
        if img is None:
            return templates.TemplateResponse(
                "index.html", 
                {"request": request, "error": "Error membaca gambar", "models": AVAILABLE_MODELS}
            )
        
        # Simpan alpha channel jika ada
        has_alpha = len(img.shape) == 3 and img.shape[2] == 4
        alpha_channel = None
        if has_alpha:
            alpha_channel = img[:, :, 3]
            img = img[:, :, :3]
        
        # Resize image if too large
        max_dimension = 512
        height, width = img.shape[:2]
        if height > max_dimension or width > max_dimension:
            scale = max_dimension / max(height, width)
            img = cv2.resize(img, (int(width * scale), int(height * scale)))
            if alpha_channel is not None:
                alpha_channel = cv2.resize(alpha_channel, (int(width * scale), int(height * scale)))
        
        # BGR to RGB
        if len(img.shape) == 3:
            img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
        
        # Get selected model
        upsampler, outscale = get_upsampler(model)
        
        # Process with RealESRGAN
        output, _ = upsampler.enhance(img, outscale=outscale)
        
        # Ensure output is in the correct format (uint8)
        output = np.clip(output, 0, 255).astype(np.uint8)
        
        # Post-processing untuk meningkatkan kualitas hasil
        # 1. Meningkatkan kontras dan kecerahan (minimal)
        alpha_contrast = 1.0  # Kontras netral
        beta = 0     # Brightness netral
        output = np.clip(output * alpha_contrast + beta, 0, 255).astype(np.uint8)
        
        # 2. Menerapkan Gaussian blur untuk menghaluskan
        output = cv2.GaussianBlur(output, (3, 3), 0.5)
        
        # 3. Apply bilateral filter untuk menghaluskan dengan tetap menjaga edge
        output = cv2.bilateralFilter(output, 9, 75, 75)
        
        # 4. Meningkatkan saturasi warna (minimal)
        hsv = cv2.cvtColor(output, cv2.COLOR_RGB2HSV)
        hsv[:,:,1] = np.clip(hsv[:,:,1] * 1.0, 0, 255)  # Tidak mengubah saturasi
        output = cv2.cvtColor(hsv, cv2.COLOR_HSV2RGB)
        
        # 5. Skip adaptive sharpening
        # RGB to BGR
        output = cv2.cvtColor(output, cv2.COLOR_RGB2BGR)
        
        # Jika gambar asli memiliki alpha channel, upscale alpha channel dan gabungkan kembali
        if has_alpha:
            # Upscale alpha channel
            alpha_upscaled = cv2.resize(alpha_channel, (output.shape[1], output.shape[0]), interpolation=cv2.INTER_LANCZOS4)
            # Tambahkan alpha channel ke output
            output = cv2.cvtColor(output, cv2.COLOR_BGR2BGRA)
            output[:, :, 3] = alpha_upscaled
        
        # Simpan gambar hasil untuk download
        output_filename = f"{uuid.uuid4()}.png"
        output_path = os.path.join(RESULT_FOLDER, output_filename)
        cv2.imwrite(output_path, output, [cv2.IMWRITE_PNG_COMPRESSION, 9])
        
        # Pastikan direktori ada
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        # Simpan gambar hasil
        _, output_buffer = cv2.imencode('.png', output)
        with open(output_path, 'wb') as f:
            f.write(output_buffer)
        
        # Convert to base64 for display in browser
        _, input_buffer = cv2.imencode('.png', cv2.cvtColor(img, cv2.COLOR_RGB2BGR) if len(img.shape) == 3 and img.shape[2] == 3 else img)
        
        input_base64 = base64.b64encode(input_buffer).decode('utf-8')
        output_base64 = base64.b64encode(output_buffer).decode('utf-8')
        
        input_data_url = f"data:image/png;base64,{input_base64}"
        output_data_url = f"data:image/png;base64,{output_base64}"
        
        # Dapatkan nama model yang digunakan
        model_name = AVAILABLE_MODELS[model]['name'] if model in AVAILABLE_MODELS else "RealESRGAN"
        
        return templates.TemplateResponse(
            "result.html", 
            {
                "request": request,
                "input_image": input_data_url,
                "output_image": output_data_url,
                "download_url": f"/download/{output_filename}",
                "model_used": model_name
            }
        )
    
    except Exception as e:
        return templates.TemplateResponse(
            "index.html", 
            {"request": request, "error": f"Error memproses gambar: {str(e)}", "models": AVAILABLE_MODELS}
        )

@app.get("/download/{filename}")
async def download_file(filename: str):
    file_path = os.path.join(RESULT_FOLDER, filename)
    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail="File tidak ditemukan")
    return FileResponse(path=file_path, filename=f"upscaled_{filename}", media_type="image/png")