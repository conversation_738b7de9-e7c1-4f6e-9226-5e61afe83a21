{% extends 'base.html' %}

{% block content %}
<div class="upload-container">
    <h2><PERSON>lih Gambar atau Video untuk diupscale</h2>
    <p class="description">Unggah gambar atau video Anda untuk ditingkatkan kualitasnya dengan teknologi AI</p>
    
    <form id="upload-form" action="/upscale" method="post" enctype="multipart/form-data">
        <div class="file-input-container">
            <label for="file" class="file-label">
                <div class="file-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                        <polyline points="17 8 12 3 7 8"></polyline>
                        <line x1="12" y1="3" x2="12" y2="15"></line>
                    </svg>
                </div>
                <span>Pilih file gambar/video atau seret ke sini</span>
                <span class="file-format">(PNG, JPG, JPEG, WEBP, MP4, AVI, MOV, MKV)</span>
            </label>
            <input type="file" id="file" name="file" accept=".png,.jpg,.jpeg,.webp,.mp4,.avi,.mov,.mkv" required>
        </div>
        
        <div id="preview-container" class="preview-container" style="display: none;">
            <h3>Preview</h3>
            <div class="preview-image-container">
                <img id="image-preview" src="" alt="Preview" style="display: none;">
                <video id="video-preview" controls style="display: none; max-width: 100%; max-height: 300px;"></video>
            </div>
        </div>
        
        <div class="model-selection-container">
            <h3>Pilih Model</h3>
            <select id="model-select" name="model" class="model-select">
                <option value="realesrgan-x4plus" selected>RealESRGAN x4plus - Model umum untuk foto, 4x upscaling</option>
                <option value="realesrgan-x2plus">RealESRGAN x2plus - Model umum untuk foto, 2x upscaling</option>
                <option value="realesrgan-anime-x4">RealESRGAN Anime x4 - Khusus untuk anime/kartun, 4x upscaling</option>
                <option value="realesrnet-x4plus">RealESRNet x4plus - Lebih ringan, 4x upscaling</option>
                <option value="realesr-general-x4v3">RealESRGAN General x4v3 - Optimized untuk video, 4x upscaling</option>
            </select>
            <div id="model-description" class="model-description">
                Model umum untuk foto, 4x upscaling
            </div>
        </div>
        
        {% if error %}
        <div class="error-message">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="10"></circle>
                <line x1="12" y1="8" x2="12" y2="12"></line>
                <line x1="12" y1="16" x2="12.01" y2="16"></line>
            </svg>
            {{ error }}
        </div>
        {% endif %}

        <button type="submit" id="submit-btn" class="submit-button">
            <span>Upscale Gambar</span>
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <line x1="5" y1="12" x2="19" y2="12"></line>
                <polyline points="12 5 19 12 12 19"></polyline>
            </svg>
        </button>
    </form>
</div>

<div class="features-container">
    <div class="feature">
        <div class="feature-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                <circle cx="8.5" cy="8.5" r="1.5"></circle>
                <polyline points="21 15 16 10 5 21"></polyline>
            </svg>
        </div>
        <h3>Memiliki Banyak Model</h3>
        <p>Tingkatkan resolusi gambar dengan banyak model, 2-4x lebih besar dari kartun maupun realistik</p>
    </div>
    <div class="feature">
        <div class="feature-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M12 2v4m0 12v4M4.93 4.93l2.83 2.83m8.48 8.48l2.83 2.83M2 12h4m12 0h4M4.93 19.07l2.83-2.83m8.48-8.48l2.83-2.83"></path>
            </svg>
        </div>
        <h3>Peningkatan Detail</h3>
        <p>Perjelas detail yang hilang dan tingkatkan ketajaman gambar</p>
    </div>
    <div class="feature">
        <div class="feature-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3"></path>
            </svg>
        </div>
        <h3>Mudah Digunakan</h3>
        <p>Cukup unggah gambar dan dapatkan hasil dalam hitungan detik</p>
    </div>
</div>
{% endblock %}