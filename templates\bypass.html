<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="ngrok-skip-browser-warning" content="true">
    <meta http-equiv="X-Ngrok-Skip-Browser-Warning" content="true">
    <title>Billy'S Upscaler - Loading...</title>
    <style>
        body {
            background: linear-gradient(135deg, #1a1a1a, #2d2d2d);
            color: white;
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .loading-container {
            text-align: center;
            max-width: 500px;
            padding: 2rem;
        }
        
        .logo {
            font-size: 2.5rem;
            color: #ff0000;
            margin-bottom: 1rem;
            font-weight: bold;
        }
        
        .loading-text {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            color: #ccc;
        }
        
        .spinner {
            width: 50px;
            height: 50px;
            border: 4px solid #333;
            border-top: 4px solid #ff0000;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 2rem;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .bypass-info {
            font-size: 0.9rem;
            color: #888;
            margin-top: 2rem;
        }
        
        .manual-link {
            display: inline-block;
            margin-top: 1rem;
            padding: 0.8rem 1.5rem;
            background-color: #ff0000;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: background-color 0.3s;
        }
        
        .manual-link:hover {
            background-color: #cc0000;
        }
    </style>
</head>
<body>
    <div class="loading-container">
        <div class="logo">Billy'S Upscaler</div>
        <div class="loading-text">Memuat aplikasi...</div>
        <div class="spinner"></div>
        <div class="bypass-info">
            Sedang melewati halaman peringatan ngrok...
            <br><br>
            <a href="/" class="manual-link" id="manual-link">Klik di sini jika tidak otomatis</a>
        </div>
    </div>

    <script>
        // Aggressive bypass script
        (function() {
            let attempts = 0;
            const maxAttempts = 10;
            
            function attemptBypass() {
                attempts++;
                console.log(`Bypass attempt ${attempts}/${maxAttempts}`);
                
                // Check if we're still on warning page
                const isWarningPage = document.body.textContent.includes('Are you the developer?') ||
                                     document.body.textContent.includes('Visit Site') ||
                                     window.location.href.includes('ngrok-free.app');
                
                if (!isWarningPage) {
                    // We're already on the app, redirect to main page
                    window.location.href = '/';
                    return;
                }
                
                // Try different bypass methods
                const methods = [
                    // Method 1: Click Visit Site button
                    () => {
                        const buttons = document.querySelectorAll('button, a, input[type="submit"]');
                        for (let button of buttons) {
                            if (button.textContent.includes('Visit Site') || 
                                button.textContent.includes('Visit') ||
                                button.value === 'Visit Site') {
                                button.click();
                                return true;
                            }
                        }
                        return false;
                    },
                    
                    // Method 2: Submit any form
                    () => {
                        const form = document.querySelector('form');
                        if (form) {
                            form.submit();
                            return true;
                        }
                        return false;
                    },
                    
                    // Method 3: Fetch with bypass headers
                    () => {
                        fetch(window.location.href, {
                            method: 'GET',
                            headers: {
                                'ngrok-skip-browser-warning': 'true',
                                'User-Agent': 'Mozilla/5.0 (BillyUpscaler) Bypass/1.0',
                                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
                            }
                        }).then(response => {
                            if (response.ok) {
                                window.location.reload();
                            }
                        }).catch(console.error);
                        return true;
                    },
                    
                    // Method 4: Direct redirect with parameters
                    () => {
                        const baseUrl = window.location.origin;
                        window.location.href = baseUrl + '/?ngrok-skip-browser-warning=true';
                        return true;
                    }
                ];
                
                // Try each method
                for (let method of methods) {
                    try {
                        if (method()) {
                            console.log('Bypass method executed');
                            break;
                        }
                    } catch (e) {
                        console.error('Bypass method failed:', e);
                    }
                }
                
                // Continue trying if we haven't reached max attempts
                if (attempts < maxAttempts) {
                    setTimeout(attemptBypass, 1000);
                } else {
                    // Show manual link
                    document.getElementById('manual-link').style.display = 'inline-block';
                    document.querySelector('.loading-text').textContent = 'Bypass otomatis gagal. Silakan klik tombol di bawah.';
                }
            }
            
            // Start bypass attempts
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', attemptBypass);
            } else {
                attemptBypass();
            }
            
            // Also try immediately and with delays
            setTimeout(attemptBypass, 100);
            setTimeout(attemptBypass, 500);
            setTimeout(attemptBypass, 1000);
            
        })();
    </script>
</body>
</html>
