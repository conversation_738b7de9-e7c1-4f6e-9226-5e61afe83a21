#!/usr/bin/env python3
"""
Automatic ngrok warning bypass utility
Run this script to automatically bypass ngrok browser warning
"""

import requests
import time
import webbrowser
from urllib.parse import urlparse

def bypass_ngrok_warning(url):
    """Bypass ngrok browser warning by making request with proper headers"""
    
    print(f"🔄 Attempting to bypass ngrok warning for: {url}")
    
    # Headers that bypass ngrok warning
    bypass_headers = {
        'ngrok-skip-browser-warning': 'true',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 BillyUpscaler/1.0',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate, br',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    }
    
    try:
        # Make request with bypass headers
        response = requests.get(url, headers=bypass_headers, timeout=10)
        
        if response.status_code == 200:
            # Check if we got the actual app or warning page
            if 'Are you the developer?' in response.text:
                print("⚠️  Still getting warning page")
                return False, "Warning page still appears"
            elif 'Billy\'S Upscaler' in response.text:
                print("✅ Successfully bypassed warning!")
                return True, "App loaded successfully"
            else:
                print("❓ Unknown response content")
                return False, "Unknown response"
        else:
            print(f"❌ HTTP {response.status_code}")
            return False, f"HTTP {response.status_code}"
            
    except Exception as e:
        print(f"❌ Request failed: {e}")
        return False, str(e)

def create_bypass_url(original_url):
    """Create URL with bypass parameters"""
    parsed = urlparse(original_url)
    
    # Add bypass parameters to URL
    if '?' in original_url:
        bypass_url = f"{original_url}&ngrok-skip-browser-warning=true"
    else:
        bypass_url = f"{original_url}?ngrok-skip-browser-warning=true"
    
    return bypass_url

def open_with_bypass(url):
    """Open URL in browser with bypass attempt"""
    
    print(f"🌐 Opening {url} with bypass...")
    
    # First, try to bypass programmatically
    success, message = bypass_ngrok_warning(url)
    
    if success:
        print("✅ Bypass successful, opening in browser...")
        # Open the original URL (app should handle bypass now)
        webbrowser.open(url)
    else:
        print(f"⚠️  Bypass attempt: {message}")
        print("🔄 Opening in browser anyway (manual bypass may be needed)")
        
        # Try opening with bypass parameters
        bypass_url = create_bypass_url(url)
        webbrowser.open(bypass_url)
        
        print("\n📝 If you still see warning page:")
        print("1. Click 'Visit Site' button")
        print("2. Or refresh the page")
        print("3. Warning only appears once per browser session")

def monitor_and_bypass(url, interval=30):
    """Monitor URL and attempt bypass periodically"""
    
    print(f"🔍 Monitoring {url} for warning bypass...")
    print(f"⏰ Checking every {interval} seconds")
    print("Press Ctrl+C to stop monitoring")
    
    try:
        while True:
            success, message = bypass_ngrok_warning(url)
            
            if success:
                print("✅ URL is accessible without warning")
            else:
                print(f"⚠️  {message} - continuing to monitor...")
            
            time.sleep(interval)
            
    except KeyboardInterrupt:
        print("\n👋 Monitoring stopped")

def main():
    """Main function with interactive menu"""
    
    print("🚀 Ngrok Warning Bypass Utility")
    print("=" * 40)
    
    # Get URL from user
    url = input("Enter ngrok URL: ").strip()
    
    if not url:
        print("❌ No URL provided")
        return
    
    if not url.startswith('http'):
        url = f"https://{url}"
    
    print(f"\n🎯 Target URL: {url}")
    
    # Menu options
    print("\nChoose action:")
    print("1. Test bypass")
    print("2. Open in browser with bypass")
    print("3. Monitor and bypass")
    print("4. Generate bypass bookmarklet")
    
    choice = input("\nEnter choice (1-4): ").strip()
    
    if choice == "1":
        success, message = bypass_ngrok_warning(url)
        print(f"Result: {message}")
        
    elif choice == "2":
        open_with_bypass(url)
        
    elif choice == "3":
        interval = input("Monitor interval in seconds (default 30): ").strip()
        interval = int(interval) if interval.isdigit() else 30
        monitor_and_bypass(url, interval)
        
    elif choice == "4":
        # Generate bookmarklet
        bookmarklet = f"""javascript:(function(){{
            if(window.location.hostname.includes('ngrok')){{
                var xhr = new XMLHttpRequest();
                xhr.open('GET', '{url}', true);
                xhr.setRequestHeader('ngrok-skip-browser-warning', 'true');
                xhr.setRequestHeader('User-Agent', 'BillyUpscaler/1.0');
                xhr.onload = function(){{ window.location.reload(); }};
                xhr.send();
            }}
        }})();"""
        
        print("\n📎 Bypass Bookmarklet:")
        print("Copy this to browser bookmarks:")
        print(bookmarklet)
        print("\nTo use: Click bookmark when on ngrok warning page")
        
    else:
        print("❌ Invalid choice")

if __name__ == "__main__":
    main()
