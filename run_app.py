#!/usr/bin/env python3
"""
Improved startup script for Billy'S Upscaler with better error handling
"""

import uvicorn
import os
import sys
import torch
from pathlib import Path

def check_dependencies():
    """Check if all required dependencies are installed"""
    required_packages = [
        'fastapi',
        'uvicorn', 
        'torch',
        'cv2',
        'numpy',
        'realesrgan',
        'basicsr'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            if package == 'cv2':
                import cv2
            else:
                __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ Missing packages: {', '.join(missing_packages)}")
        print("Please install them with: pip install -r requirements.txt")
        return False
    
    print("✅ All dependencies are installed")
    return True

def check_gpu():
    """Check GPU availability and configuration"""
    if torch.cuda.is_available():
        gpu_name = torch.cuda.get_device_name(0)
        print(f"✅ GPU detected: {gpu_name}")
        
        # GPU optimizations
        torch.cuda.empty_cache()
        torch.backends.cuda.matmul.allow_tf32 = True
        torch.backends.cudnn.allow_tf32 = True
        torch.backends.cudnn.benchmark = True
        
        return True
    else:
        print("⚠️  No GPU detected, using CPU (processing will be slower)")
        return False

def test_model_loading():
    """Test if RealESRGAN models can be loaded"""
    try:
        from realesrgan import RealESRGANer
        from basicsr.archs.rrdbnet_arch import RRDBNet
        
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # Test with minimal configuration
        model = RRDBNet(num_in_ch=3, num_out_ch=3, num_feat=64, num_block=23, num_grow_ch=32, scale=4)
        
        upsampler = RealESRGANer(
            scale=4,
            model_path='https://github.com/xinntao/Real-ESRGAN/releases/download/v0.1.0/RealESRGAN_x4plus.pth',
            model=model,
            tile=512,
            tile_pad=16,
            pre_pad=10,
            half=(device.type == 'cuda'),
            device=device
        )
        
        print("✅ RealESRGAN model loading test successful")
        return True
        
    except Exception as e:
        print(f"⚠️  Model loading test failed: {e}")
        print("The application will continue but may have issues with image processing")
        return False

def setup_directories():
    """Create necessary directories"""
    directories = [
        'static/uploads',
        'static/results', 
        'weights'
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    print("✅ Directories created/verified")

def start_with_ngrok():
    """Start application with ngrok tunnel"""
    try:
        from pyngrok import ngrok

        port = 8000

        # Create ngrok tunnel
        print("🌐 Creating ngrok tunnel...")
        tunnel = ngrok.connect(port, "http")
        public_url = tunnel.public_url

        print(f"🚀 Application accessible at: {public_url}")
        print("📝 Note: Click 'Visit Site' if you see ngrok warning page")

        # Set environment variable for the app
        os.environ["BASE_URL"] = public_url

        # Start the application
        print("🔄 Starting FastAPI server...")
        uvicorn.run("app:app", host="0.0.0.0", port=port, reload=False)

    except ImportError:
        print("❌ pyngrok not installed. Install with: pip install pyngrok")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Error starting with ngrok: {e}")
        print("Trying to start without ngrok...")
        start_local()

def start_local():
    """Start application locally without ngrok"""
    port = 8000
    print(f"🚀 Starting application locally at: http://localhost:{port}")
    print("📝 Note: Application will only be accessible locally")
    
    try:
        uvicorn.run("app:app", host="127.0.0.1", port=port, reload=True)
    except Exception as e:
        print(f"❌ Error starting local server: {e}")
        sys.exit(1)

def main():
    """Main startup function"""
    print("🔧 Billy'S Upscaler - Starting Application")
    print("=" * 50)
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    # Setup directories
    setup_directories()
    
    # Check GPU
    check_gpu()
    
    # Test model loading
    test_model_loading()
    
    print("\n" + "=" * 50)
    print("🚀 Starting web server...")
    
    # Ask user for startup method
    choice = input("\nChoose startup method:\n1. With ngrok (external access)\n2. Local only\nEnter choice (1 or 2): ").strip()
    
    if choice == "1":
        start_with_ngrok()
    elif choice == "2":
        start_local()
    else:
        print("Invalid choice. Starting with ngrok by default...")
        start_with_ngrok()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 Application stopped by user")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)
