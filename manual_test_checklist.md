# BLACK BOX TESTING CHECKLIST - BILLY'S UPSCALER

## Test Environment Setup
- [ ] Application URL: ________________
- [ ] Browser: ________________
- [ ] Screen Resolution: ________________
- [ ] Test Date: ________________

## 1. UI/UX TESTING

### Test Case 1.1: Homepage Loading
- [ ] Navigate to application URL
- [ ] Page loads within 5 seconds
- [ ] Title shows "BillUpscaler"
- [ ] Header displays "Billy'S Upscaler"
- [ ] Tagline "Tingkatkan kualitas gambar dengan AI" visible
- [ ] Navigation menu shows "Home" and "About"
- [ ] Upload area is visible
- [ ] Model selection dropdown is present
- **Result**: ⬜ PASS ⬜ FAIL
- **Notes**: ________________________________

### Test Case 1.2: Navigation Testing
- [ ] Click "About" link
- [ ] About page loads successfully
- [ ] Page contains information about Real-ESRGAN
- [ ] Credits section is visible
- [ ] Click "Home" link to return
- [ ] Returns to homepage successfully
- **Result**: ⬜ PASS ⬜ FAIL
- **Notes**: ________________________________

### Test Case 1.3: Responsive Design
- [ ] Test on desktop (full screen)
- [ ] Test on tablet size (resize browser to ~768px width)
- [ ] Test on mobile size (resize browser to ~375px width)
- [ ] All elements remain accessible
- [ ] Text remains readable
- [ ] Buttons remain clickable
- **Result**: ⬜ PASS ⬜ FAIL
- **Notes**: ________________________________

## 2. FILE UPLOAD TESTING

### Test Case 2.1: Valid Image Upload
**Test with PNG file (< 5MB)**
- [ ] Click file input or drag file to upload area
- [ ] Select valid PNG image
- [ ] Preview appears immediately
- [ ] File name is displayed
- [ ] No error messages
- **Result**: ⬜ PASS ⬜ FAIL

**Test with JPG file (< 5MB)**
- [ ] Upload JPG image
- [ ] Preview appears
- [ ] No error messages
- **Result**: ⬜ PASS ⬜ FAIL

**Test with WEBP file (< 5MB)**
- [ ] Upload WEBP image
- [ ] Preview appears
- [ ] No error messages
- **Result**: ⬜ PASS ⬜ FAIL

### Test Case 2.2: Valid Video Upload
**Test with MP4 file (< 100MB)**
- [ ] Upload MP4 video
- [ ] Video preview appears with controls
- [ ] Can play preview
- [ ] No error messages
- **Result**: ⬜ PASS ⬜ FAIL

### Test Case 2.3: Invalid File Types
**Test with TXT file**
- [ ] Try to upload .txt file
- [ ] Error message appears: "Tipe file tidak valid"
- [ ] Upload is rejected
- **Result**: ⬜ PASS ⬜ FAIL

**Test with PDF file**
- [ ] Try to upload .pdf file
- [ ] Error message appears
- [ ] Upload is rejected
- **Result**: ⬜ PASS ⬜ FAIL

### Test Case 2.4: File Size Limits
**Test with large image (> 5MB)**
- [ ] Try to upload image larger than 5MB
- [ ] Error message about file size appears
- [ ] Upload is rejected
- **Result**: ⬜ PASS ⬜ FAIL

**Test with large video (> 100MB)**
- [ ] Try to upload video larger than 100MB
- [ ] Error message about file size appears
- [ ] Upload is rejected
- **Result**: ⬜ PASS ⬜ FAIL

## 3. MODEL SELECTION TESTING

### Test Case 3.1: Model Selection Dropdown
- [ ] Click model selection dropdown
- [ ] "RealESRGAN x4plus" is available
- [ ] "RealESRGAN x2plus" is available
- [ ] "RealESRGAN Anime x4" is available
- [ ] "RealESRNet x4plus" is available
- [ ] "RealESRGAN General x4v3" is available
- [ ] Default selection is "RealESRGAN x4plus"
- **Result**: ⬜ PASS ⬜ FAIL

### Test Case 3.2: Model Description Update
- [ ] Select "RealESRGAN x2plus"
- [ ] Description updates to show "2x upscaling"
- [ ] Select "Anime x4"
- [ ] Description updates to show "anime/kartun"
- [ ] Select "General x4v3"
- [ ] Description updates to show "video"
- **Result**: ⬜ PASS ⬜ FAIL

## 4. IMAGE PROCESSING TESTING

### Test Case 4.1: Standard Image Upscaling
**Prepare test image: Small PNG (256x256 or similar)**
- [ ] Upload test image
- [ ] Select "RealESRGAN x4plus"
- [ ] Click "Upscale Gambar"
- [ ] Loading animation appears
- [ ] Processing completes (wait up to 2 minutes)
- [ ] Result page loads
- [ ] Before/after comparison is visible
- [ ] Download link is present
- [ ] Can download result
- **Processing Time**: _______ seconds
- **Result**: ⬜ PASS ⬜ FAIL

### Test Case 4.2: Different Model Comparison
**Use same test image**
- [ ] Process with "RealESRGAN x2plus"
- [ ] Note result quality and processing time
- [ ] Process with "RealESRGAN Anime x4"
- [ ] Note result quality and processing time
- [ ] Results show visible differences
- **Result**: ⬜ PASS ⬜ FAIL

### Test Case 4.3: Transparent Images
**Use PNG with transparency**
- [ ] Upload PNG with transparent background
- [ ] Process with any model
- [ ] Download result
- [ ] Verify transparency is preserved
- **Result**: ⬜ PASS ⬜ FAIL

## 5. VIDEO PROCESSING TESTING

### Test Case 5.1: Short Video Processing
**Use short video (5-10 seconds, low resolution)**
- [ ] Upload short video
- [ ] Select "RealESRGAN General x4v3" (video optimized)
- [ ] Click "Upscale Gambar"
- [ ] Processing starts (may take several minutes)
- [ ] Result page shows video thumbnails
- [ ] Can download processed video
- **Processing Time**: _______ minutes
- **Result**: ⬜ PASS ⬜ FAIL

## 6. ERROR HANDLING TESTING

### Test Case 6.1: No File Selected
- [ ] Don't select any file
- [ ] Click "Upscale Gambar"
- [ ] Error message appears: "Tidak ada file yang dipilih"
- **Result**: ⬜ PASS ⬜ FAIL

### Test Case 6.2: Corrupted Files
- [ ] Create corrupted image file (rename .txt to .png)
- [ ] Try to upload corrupted file
- [ ] Appropriate error message appears
- **Result**: ⬜ PASS ⬜ FAIL

## 7. PERFORMANCE TESTING

### Test Case 7.1: Processing Time Measurement
**Record processing times for different file sizes:**
- Small image (256x256): _______ seconds
- Medium image (512x512): _______ seconds
- Large image (1024x1024): _______ seconds
- Short video (5 sec): _______ minutes

**Acceptable Performance:**
- [ ] Small images process in < 30 seconds
- [ ] Medium images process in < 60 seconds
- [ ] Large images process in < 120 seconds
- **Result**: ⬜ PASS ⬜ FAIL

## 8. CROSS-BROWSER COMPATIBILITY

### Test Case 8.1: Browser Testing
**Test in Chrome:**
- [ ] All features work correctly
- [ ] File upload works
- [ ] Processing works
- [ ] Download works
- **Result**: ⬜ PASS ⬜ FAIL

**Test in Firefox:**
- [ ] All features work correctly
- [ ] File upload works
- [ ] Processing works
- [ ] Download works
- **Result**: ⬜ PASS ⬜ FAIL

**Test in Edge:**
- [ ] All features work correctly
- [ ] File upload works
- [ ] Processing works
- [ ] Download works
- **Result**: ⬜ PASS ⬜ FAIL

## 9. SECURITY TESTING

### Test Case 9.1: File Upload Security
- [ ] Try to upload .exe file - should be rejected
- [ ] Try to upload .js file - should be rejected
- [ ] Try to upload .php file - should be rejected
- [ ] Only allowed extensions accepted
- **Result**: ⬜ PASS ⬜ FAIL

## 10. USABILITY TESTING

### Test Case 10.1: First-Time User Experience
**Simulate first-time user:**
- [ ] Instructions are clear
- [ ] Upload process is intuitive
- [ ] Model selection is understandable
- [ ] Processing feedback is adequate
- [ ] Result presentation is clear
- [ ] Download process is obvious
- **Result**: ⬜ PASS ⬜ FAIL

## OVERALL TEST SUMMARY

**Total Test Cases**: _______
**Passed**: _______
**Failed**: _______
**Success Rate**: _______%

**Critical Issues Found:**
1. ________________________________
2. ________________________________
3. ________________________________

**Recommendations:**
1. ________________________________
2. ________________________________
3. ________________________________

**Tester**: ________________
**Date Completed**: ________________
**Overall Assessment**: ⬜ ACCEPTABLE ⬜ NEEDS IMPROVEMENT ⬜ MAJOR ISSUES
