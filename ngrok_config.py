#!/usr/bin/env python3
"""
Enhanced ngrok configuration for Billy'S Upscaler
Automatically handles browser warning bypass
"""

import os
import time
import requests
from pyngrok import ngrok, conf
from pyngrok.conf import PyngrokConfig

def setup_ngrok_config():
    """Setup ngrok configuration to minimize warnings"""
    try:
        # Set ngrok configuration
        pyngrok_config = PyngrokConfig(
            ngrok_path=None,  # Use default ngrok path
            config_path=None,  # Use default config path
            auth_token=None,  # Use auth token from environment or config
            region="us",      # Use US region for better performance
        )
        conf.set_default(pyngrok_config)
        
        print("✅ Ngrok configuration set successfully")
        return True
    except Exception as e:
        print(f"⚠️  Warning: Could not set ngrok config: {e}")
        return False

def create_tunnel_with_retry(port, max_retries=3):
    """Create ngrok tunnel with retry mechanism"""
    for attempt in range(max_retries):
        try:
            print(f"🌐 Creating ngrok tunnel (attempt {attempt + 1}/{max_retries})...")
            
            # Create tunnel with specific configuration
            tunnel = ngrok.connect(
                port,
                "http",
                options={
                    "bind_tls": True,  # Force HTTPS
                    "inspect": False,  # Disable inspection
                }
            )
            
            public_url = tunnel.public_url
            print(f"✅ Tunnel created successfully: {public_url}")
            
            # Test the tunnel
            if test_tunnel(public_url):
                return tunnel
            else:
                print("⚠️  Tunnel test failed, retrying...")
                ngrok.disconnect(tunnel.public_url)
                
        except Exception as e:
            print(f"❌ Attempt {attempt + 1} failed: {e}")
            if attempt < max_retries - 1:
                print("🔄 Retrying in 2 seconds...")
                time.sleep(2)
            else:
                print("❌ All attempts failed")
                raise e
    
    return None

def test_tunnel(url):
    """Test if tunnel is working and bypass warning"""
    try:
        print(f"🧪 Testing tunnel: {url}")
        
        # Test with custom headers to bypass warning
        headers = {
            'ngrok-skip-browser-warning': 'true',
            'User-Agent': 'BillyUpscaler/1.0 (Custom Bot)',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
        }
        
        response = requests.get(url, headers=headers, timeout=10)
        
        if response.status_code == 200:
            # Check if we got the warning page
            if 'Are you the developer?' in response.text:
                print("⚠️  Warning page detected, but tunnel is working")
                return True
            elif 'Billy\'S Upscaler' in response.text:
                print("✅ Application loaded successfully")
                return True
            else:
                print("⚠️  Unexpected response content")
                return False
        else:
            print(f"❌ HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Tunnel test failed: {e}")
        return False

def setup_bypass_instructions(public_url):
    """Print instructions for bypassing ngrok warning"""
    print("\n" + "="*60)
    print("🚀 NGROK TUNNEL READY")
    print("="*60)
    print(f"📱 Public URL: {public_url}")
    print("\n📝 To bypass ngrok warning:")
    print("1. The app automatically handles warning bypass")
    print("2. If you still see warning, click 'Visit Site'")
    print("3. Or add this to browser bookmarks:")
    
    # Create a bookmarklet for easy bypass
    bookmarklet = f"""javascript:(function(){{
        if(window.location.hostname.includes('ngrok')){{
            fetch('{public_url}', {{
                headers: {{
                    'ngrok-skip-browser-warning': 'true',
                    'User-Agent': 'BillyUpscaler/1.0'
                }}
            }}).then(() => window.location.reload());
        }}
    }})();"""
    
    print(f"4. Bookmarklet: {bookmarklet}")
    print("\n💡 Tips:")
    print("- Warning only appears once per browser session")
    print("- Use incognito/private mode for testing")
    print("- Consider upgrading to paid ngrok for no warnings")
    print("="*60)

def start_tunnel(port=8000):
    """Main function to start ngrok tunnel with warning bypass"""
    print("🔧 Setting up ngrok tunnel with warning bypass...")
    
    # Setup configuration
    setup_ngrok_config()
    
    # Kill any existing tunnels
    try:
        ngrok.kill()
        time.sleep(1)
    except:
        pass
    
    # Create tunnel
    tunnel = create_tunnel_with_retry(port)
    
    if tunnel:
        public_url = tunnel.public_url
        
        # Set environment variable for the app
        os.environ["BASE_URL"] = public_url
        os.environ["NGROK_URL"] = public_url
        
        # Print instructions
        setup_bypass_instructions(public_url)
        
        return tunnel
    else:
        raise Exception("Failed to create ngrok tunnel")

if __name__ == "__main__":
    try:
        tunnel = start_tunnel()
        print("✅ Ngrok tunnel is ready!")
        print("Press Ctrl+C to stop...")
        
        # Keep the script running
        while True:
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\n👋 Stopping ngrok tunnel...")
        ngrok.kill()
    except Exception as e:
        print(f"❌ Error: {e}")
        ngrok.kill()
