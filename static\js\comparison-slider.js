// Image Comparison Slider
document.addEventListener('DOMContentLoaded', function() {
    const slider = document.getElementById('comparison-slider');
    if (!slider) return;
    
    const beforeImage = slider.querySelector('.before-image');
    const afterImage = slider.querySelector('.after-image');
    const sliderHandle = slider.querySelector('.slider-handle');
    
    let isDragging = false;
    
    // Function to set slider position
    function setSliderPosition(position) {
        const sliderWidth = slider.offsetWidth;
        let newPosition = position;
        
        // Limit position within slider range
        if (newPosition < 0) newPosition = 0;
        if (newPosition > sliderWidth) newPosition = sliderWidth;
        
        // Calculate percentage
        const percentage = (newPosition / sliderWidth) * 100;
        
        // Set after image width
        afterImage.style.width = `${percentage}%`;
        
        // Position handle
        sliderHandle.style.left = `${percentage}%`;
    }
    
    // Mouse down event on handle
    sliderHandle.addEventListener('mousedown', function(e) {
        isDragging = true;
        e.preventDefault();
    });
    
    // Touch start event on handle
    sliderHandle.addEventListener('touchstart', function(e) {
        isDragging = true;
    });
    
    // Mouse move event
    document.addEventListener('mousemove', function(e) {
        if (!isDragging) return;
        
        const sliderRect = slider.getBoundingClientRect();
        const position = e.clientX - sliderRect.left;
        
        setSliderPosition(position);
    });
    
    // Touch move event
    document.addEventListener('touchmove', function(e) {
        if (!isDragging || !e.touches[0]) return;
        
        const sliderRect = slider.getBoundingClientRect();
        const position = e.touches[0].clientX - sliderRect.left;
        
        setSliderPosition(position);
    });
    
    // Mouse up event
    document.addEventListener('mouseup', function() {
        isDragging = false;
    });
    
    // Touch end event
    document.addEventListener('touchend', function() {
        isDragging = false;
    });
    
    // Click event on slider
    slider.addEventListener('click', function(e) {
        const sliderRect = slider.getBoundingClientRect();
        const position = e.clientX - sliderRect.left;
        
        setSliderPosition(position);
    });
    
    // Set initial position (50%)
    setSliderPosition(slider.offsetWidth / 2);
});