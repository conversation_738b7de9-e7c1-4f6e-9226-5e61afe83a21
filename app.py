import os
import uuid
import base64
from fastapi import FastAP<PERSON>, UploadFile, File, Request, Form, HTTPException
from fastapi.responses import HTMLResponse, FileResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.middleware.cors import CORSMiddleware
import cv2
import numpy as np
import torch
from realesrgan import RealESRGANer
from basicsr.archs.rrdbnet_arch import RRDBNet
import logging

# Menonaktifkan log dari RealESRGAN
logging.getLogger("basicsr").setLevel(logging.WARNING)
logging.getLogger("realesrgan").setLevel(logging.WARNING)

app = FastAPI(title="Billy'S Upscaler")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Configure upload folder
UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'static', 'uploads')
RESULT_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'static', 'results')

# Tambahkan ekstensi video yang didukung
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'webp', 'mp4', 'avi', 'mov', 'mkv'}

# Ensure directories exist
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs(RESULT_FOLDER, exist_ok=True)

# Mount static files
app.mount("/static", StaticFiles(directory="static"), name="static")

# Templates
templates = Jinja2Templates(directory="templates")

# Cek ketersediaan GPU dan tampilkan informasi
if torch.cuda.is_available():
    gpu_name = torch.cuda.get_device_name(0)
    print(f"Menggunakan GPU: {gpu_name}")
    # Mengatur memori GPU untuk optimasi
    torch.cuda.empty_cache()
    # Mengatur untuk menggunakan TensorFloat32 pada GPU Ampere atau yang lebih baru (seperti RTX 4060)
    torch.backends.cuda.matmul.allow_tf32 = True
    torch.backends.cudnn.allow_tf32 = True
    # Mengaktifkan cudnn benchmark untuk performa yang lebih baik
    torch.backends.cudnn.benchmark = True
else:
    print("GPU tidak tersedia, menggunakan CPU")

# Initialize RealESRGAN model
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

# Definisikan model-model yang tersedia
AVAILABLE_MODELS = {
    'realesrgan-x4plus': {
        'name': 'RealESRGAN x4plus',
        'scale': 4,
        'model_path': 'https://github.com/xinntao/Real-ESRGAN/releases/download/v0.1.0/RealESRGAN_x4plus.pth',
        'model': lambda: RRDBNet(num_in_ch=3, num_out_ch=3, num_feat=64, num_block=23, num_grow_ch=32, scale=4)
    },
    'realesrgan-x2plus': {
        'name': 'RealESRGAN x2plus',
        'scale': 2,
        'model_path': 'https://github.com/xinntao/Real-ESRGAN/releases/download/v0.2.1/RealESRGAN_x2plus.pth',
        'model': lambda: RRDBNet(num_in_ch=3, num_out_ch=3, num_feat=64, num_block=23, num_grow_ch=32, scale=2)
    },
    'realesrgan-anime-x4': {
        'name': 'RealESRGAN Anime x4',
        'scale': 4,
        'model_path': 'https://github.com/xinntao/Real-ESRGAN/releases/download/v0.2.2.4/RealESRGAN_x4plus_anime_6B.pth',
        'model': lambda: RRDBNet(num_in_ch=3, num_out_ch=3, num_feat=64, num_block=6, num_grow_ch=32, scale=4)
    },
    'realesrnet-x4plus': {
        'name': 'RealESRNet x4plus',
        'scale': 4,
        'model_path': 'https://github.com/xinntao/Real-ESRGAN/releases/download/v0.1.1/RealESRNet_x4plus.pth',
        'model': lambda: RRDBNet(num_in_ch=3, num_out_ch=3, num_feat=64, num_block=23, num_grow_ch=32, scale=4)
    },
    'realesr-general-x4v3': {
        'name': 'RealESRGAN General x4v3',
        'scale': 4,
        'model_path': 'https://github.com/xinntao/Real-ESRGAN/releases/download/v0.2.5.0/realesr-general-x4v3.pth',
        'model': lambda: RRDBNet(num_in_ch=3, num_out_ch=3, num_feat=64, num_block=23, num_grow_ch=32, scale=4)
    },
    'realesrgan-x8plus': {
        'name': 'RealESRGAN x8plus',
        'scale': 8,
        'model_path': 'https://github.com/xinntao/Real-ESRGAN/releases/download/v0.2.2.4/RealESRGAN_x8plus.pth',
        'model': lambda: RRDBNet(num_in_ch=3, num_out_ch=3, num_feat=64, num_block=23, num_grow_ch=32, scale=8)
    },
    'realesrgan-animevideo-x4': {
        'name': 'RealESRGAN AnimeVideo x4',
        'scale': 4,
        'model_path': 'https://github.com/xinntao/Real-ESRGAN/releases/download/v0.2.2.4/RealESRGAN_x4plus_anime_6B.pth',
        'model': lambda: RRDBNet(num_in_ch=3, num_out_ch=3, num_feat=64, num_block=6, num_grow_ch=32, scale=4)
    }
}

# Inisialisasi dictionary untuk menyimpan model yang sudah dimuat
loaded_models = {}

def get_upsampler(model_name):
    """Mendapatkan atau memuat model upsampler berdasarkan nama model"""
    if model_name not in AVAILABLE_MODELS:
        model_name = 'realesrgan-x4plus'  # Default model
    
    if model_name not in loaded_models:
        model_info = AVAILABLE_MODELS[model_name]
        model = model_info['model']()

        # Adjust tile size based on model scale to prevent VRAM issues
        tile_size = 512
        if model_info['scale'] == 8:
            tile_size = 256  # Smaller tiles for 8x models to save VRAM
        elif model_info['scale'] == 4:
            tile_size = 512
        elif model_info['scale'] == 2:
            tile_size = 1024

        loaded_models[model_name] = RealESRGANer(
            scale=model_info['scale'],
            model_path=model_info['model_path'],
            model=model,
            tile=tile_size,
            tile_pad=16,
            pre_pad=10,
            half=True,
            device=device,
        )
    
    return loaded_models[model_name], AVAILABLE_MODELS[model_name]['scale']

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@app.get("/favicon.ico")
async def favicon():
    return FileResponse("static/favicon.svg", media_type="image/svg+xml")

@app.get("/", response_class=HTMLResponse)
async def index(request: Request):
    return templates.TemplateResponse("index.html", {"request": request, "models": AVAILABLE_MODELS})

@app.get("/about", response_class=HTMLResponse)
async def about(request: Request):
    return templates.TemplateResponse("about.html", {"request": request})



@app.get("/favicon.ico")
async def favicon():
    return FileResponse(os.path.join("static", "favicon.ico"), media_type="image/x-icon")

@app.post("/upscale")
async def upscale(request: Request, file: UploadFile = File(...), model: str = Form("realesrgan-x4plus")):
    if not file:
        return templates.TemplateResponse("index.html", {"request": request, "error": "Tidak ada file yang dipilih"})
    
    if not allowed_file(file.filename):
        return templates.TemplateResponse("index.html", {"request": request, "error": "Tipe file tidak valid. Silakan unggah file PNG, JPG, JPEG, WEBP, MP4, AVI, MOV, atau MKV."})
    
    try:
        # Cek apakah file adalah video
        is_video = file.filename.rsplit('.', 1)[1].lower() in {'mp4', 'avi', 'mov', 'mkv'}
        
        # Simpan file sementara
        file_extension = file.filename.rsplit('.', 1)[1].lower()
        input_filename = f"{uuid.uuid4()}.{file_extension}"
        input_path = os.path.join(UPLOAD_FOLDER, input_filename)
        
        # Baca konten file
        contents = await file.read()
        
        # Batasi ukuran file (untuk gambar 5MB, untuk video 100MB)
        max_size = 100 * 1024 * 1024 if is_video else 5 * 1024 * 1024
        if len(contents) > max_size:
            max_size_mb = max_size // (1024 * 1024)
            return templates.TemplateResponse("index.html", {"request": request, "error": f"Ukuran file terlalu besar. Maksimum {max_size_mb}MB."})
        
        # Simpan file
        with open(input_path, "wb") as f:
            f.write(contents)
        
        if is_video:
            # Proses video
            output_filename = f"{uuid.uuid4()}.mp4"
            output_path = os.path.join(RESULT_FOLDER, output_filename)
            
            # Buka video
            cap = cv2.VideoCapture(input_path)
            if not cap.isOpened():
                return templates.TemplateResponse("index.html", {"request": request, "error": "Error membuka video"})
            
            # Dapatkan properti video
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            fps = cap.get(cv2.CAP_PROP_FPS)
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            
            # Resize jika terlalu besar
            max_dimension = 512
            if height > max_dimension or width > max_dimension:
                scale = max_dimension / max(height, width)
                width = int(width * scale)
                height = int(height * scale)
            
            # Dapatkan upsampler
            upsampler, outscale = get_upsampler(model)
            
            # Hitung dimensi output
            output_width = int(width * outscale)
            output_height = int(height * outscale)
            
            # Buat VideoWriter
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(output_path, fourcc, fps, (output_width, output_height))
            
            # Proses setiap frame
            frame_count = 0
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                # Resize frame jika perlu
                if height > max_dimension or width > max_dimension:
                    frame = cv2.resize(frame, (width, height))
                
                # Konversi ke RGB untuk pemrosesan
                rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                
                # Upscale frame
                output, _ = upsampler.enhance(rgb_frame, outscale=outscale)
                
                # Konversi kembali ke BGR untuk penyimpanan
                output = cv2.cvtColor(output, cv2.COLOR_RGB2BGR)
                
                # Tulis frame ke output
                out.write(output)
                
                frame_count += 1
                print(f"Memproses frame {frame_count}/{total_frames}")
            
            # Tutup video
            cap.release()
            out.release()
            
            # Buat thumbnail untuk preview
            cap = cv2.VideoCapture(input_path)
            ret, thumbnail_frame = cap.read()
            cap.release()
            
            if ret:
                # Simpan thumbnail
                thumbnail_path = os.path.join(UPLOAD_FOLDER, f"thumb_{input_filename}.jpg")
                cv2.imwrite(thumbnail_path, thumbnail_frame)
                
                # Buat thumbnail untuk hasil
                cap = cv2.VideoCapture(output_path)
                ret, output_thumbnail = cap.read()
                cap.release()
                
                if ret:
                    output_thumbnail_path = os.path.join(RESULT_FOLDER, f"thumb_{output_filename}.jpg")
                    cv2.imwrite(output_thumbnail_path, output_thumbnail)
                    
                    # Konversi thumbnail ke base64
                    _, input_buffer = cv2.imencode('.jpg', thumbnail_frame)
                    _, output_buffer = cv2.imencode('.jpg', output_thumbnail)
                    
                    input_base64 = base64.b64encode(input_buffer).decode('utf-8')
                    output_base64 = base64.b64encode(output_buffer).decode('utf-8')
                    
                    input_data_url = f"data:image/jpeg;base64,{input_base64}"
                    output_data_url = f"data:image/jpeg;base64,{output_base64}"
                    
                    # Dapatkan nama model yang digunakan
                    model_used = AVAILABLE_MODELS[model]['name'] if model in AVAILABLE_MODELS else "RealESRGAN"
                    
                    return templates.TemplateResponse(
                        "result.html", 
                        {
                            "request": request,
                            "input_image": input_data_url,
                            "output_image": output_data_url,
                            "download_url": f"/download/{output_filename}",
                            "model_used": model_used,
                            "is_video": True
                        }
                    )
        else:
            # Proses gambar
            # Convert to numpy array
            nparr = np.frombuffer(contents, np.uint8)
            img = cv2.imdecode(nparr, cv2.IMREAD_UNCHANGED)
            
            if img is None:
                return templates.TemplateResponse(
                    "index.html", 
                    {"request": request, "error": "Error membaca gambar", "models": AVAILABLE_MODELS}
                )
            
            # Simpan alpha channel jika ada
            has_alpha = len(img.shape) == 3 and img.shape[2] == 4
            alpha_channel = None
            if has_alpha:
                alpha_channel = img[:, :, 3]
                img = img[:, :, :3]
            
            # Resize image if too large
            max_dimension = 512
            height, width = img.shape[:2]
            if height > max_dimension or width > max_dimension:
                scale = max_dimension / max(height, width)
                img = cv2.resize(img, (int(width * scale), int(height * scale)))
                if alpha_channel is not None:
                    alpha_channel = cv2.resize(alpha_channel, (int(width * scale), int(height * scale)))
            
            # BGR to RGB
            if len(img.shape) == 3:
                img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
            
            # Dapatkan model yang dipilih
            upsampler, outscale = get_upsampler(model)
            
            # Process with RealESRGAN
            output, _ = upsampler.enhance(img, outscale=outscale)
            
            # Ensure output is in the correct format (uint8)
            output = np.clip(output, 0, 255).astype(np.uint8)
            
            # Post-processing untuk meningkatkan kualitas hasil
            # 1. Meningkatkan kontras dan kecerahan (minimal)
            alpha_contrast = 1.0  # Kontras netral
            beta = 0     # Brightness netral
            output = np.clip(output * alpha_contrast + beta, 0, 255).astype(np.uint8)
            
            # 2. Menerapkan Gaussian blur untuk menghaluskan
            output = cv2.GaussianBlur(output, (3, 3), 0.5)
            
            # 3. Apply bilateral filter untuk menghaluskan dengan tetap menjaga edge
            output = cv2.bilateralFilter(output, 9, 75, 75)
            
            # 4. Meningkatkan saturasi warna (minimal)
            hsv = cv2.cvtColor(output, cv2.COLOR_RGB2HSV)
            hsv[:,:,1] = np.clip(hsv[:,:,1] * 1.0, 0, 255)  # Tidak mengubah saturasi
            output = cv2.cvtColor(hsv, cv2.COLOR_HSV2RGB)
            
            # 5. Skip adaptive sharpening
            # RGB to BGR
            output = cv2.cvtColor(output, cv2.COLOR_RGB2BGR)
                
            # Jika gambar asli memiliki alpha channel, upscale alpha channel dan gabungkan kembali
            if has_alpha:
                # Upscale alpha channel
                alpha_upscaled = cv2.resize(alpha_channel, (output.shape[1], output.shape[0]), interpolation=cv2.INTER_LANCZOS4)
                # Tambahkan alpha channel ke output
                output = cv2.cvtColor(output, cv2.COLOR_BGR2BGRA)
                output[:, :, 3] = alpha_upscaled
            
            # Simpan gambar hasil untuk download
            output_filename = f"{uuid.uuid4()}.png"
            output_path = os.path.join(RESULT_FOLDER, output_filename)
            cv2.imwrite(output_path, output, [cv2.IMWRITE_PNG_COMPRESSION, 9])
            
            # Convert to base64 for display in browser
            _, input_buffer = cv2.imencode('.png', cv2.cvtColor(img, cv2.COLOR_RGB2BGR) if len(img.shape) == 3 and img.shape[2] == 3 else img)
            _, output_buffer = cv2.imencode('.png', output)
            
            input_base64 = base64.b64encode(input_buffer).decode('utf-8')
            output_base64 = base64.b64encode(output_buffer).decode('utf-8')
            
            input_data_url = f"data:image/png;base64,{input_base64}"
            output_data_url = f"data:image/png;base64,{output_base64}"
            
            # Dapatkan nama model yang digunakan
            model_name = AVAILABLE_MODELS[model]['name'] if model in AVAILABLE_MODELS else "RealESRGAN"
            
            return templates.TemplateResponse(
                "result.html", 
                {
                    "request": request,
                    "input_image": input_data_url,
                    "output_image": output_data_url,
                    "download_url": f"/download/{output_filename}",
                    "model_used": model_name,
                    "is_video": False
                }
            )
    
    except Exception as e:
        return templates.TemplateResponse(
            "index.html", 
            {"request": request, "error": f"Error memproses file: {str(e)}", "models": AVAILABLE_MODELS}
        )

@app.get("/download/{filename}")
async def download_file(filename: str):
    file_path = os.path.join(RESULT_FOLDER, filename)
    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail="File tidak ditemukan")
    
    # Determine media type based on file extension
    media_type = "image/png"
    if filename.endswith(".mp4"):
        media_type = "video/mp4"
    elif filename.endswith(".avi"):
        media_type = "video/x-msvideo"
    elif filename.endswith(".mov"):
        media_type = "video/quicktime"
    elif filename.endswith(".mkv"):
        media_type = "video/x-matroska"
    
    return FileResponse(path=file_path, filename=f"upscaled_{filename}", media_type=media_type)