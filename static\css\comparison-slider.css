/* Image Comparison Slider Styles */
.comparison-slider-wrapper {
    position: relative;
    width: 100%;
    max-width: 800px;
    margin: 2rem auto;
    overflow: hidden;
}

.comparison-slider {
    position: relative;
    width: 100%;
    height: 400px;
    border: 2px solid var(--primary-color);
    border-radius: 8px;
    overflow: hidden;
    cursor: ew-resize;
}

.before-image, .after-image {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    overflow: hidden;
}

.before-image {
    width: 100%; /* Make sure before-image covers the entire container */
    z-index: 1; /* Lower z-index so it stays behind */
}

.after-image {
    width: 50%;
    z-index: 2; /* Higher z-index to appear on top */
    border-right: 2px solid var(--primary-color);
}

.before-image img, .after-image img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.slider-handle {
    position: absolute;
    top: 0;
    left: 50%;
    width: 4px;
    height: 100%;
    background-color: var(--primary-color);
    cursor: ew-resize;
    transform: translateX(-50%);
    z-index: 3; /* Highest z-index to stay on top */
}

.slider-handle::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 30px;
    height: 30px;
    background-color: var(--primary-color);
    border-radius: 50%;
}

.slider-handle::after {
    content: '⟷';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 16px;
    font-weight: bold;
}

.slider-labels {
    display: flex;
    justify-content: space-between;
    margin-top: 0.5rem;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .comparison-slider {
        height: 300px;
    }
}